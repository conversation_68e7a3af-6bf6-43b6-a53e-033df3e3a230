import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/core/routing/app_router.dart';
import 'package:flutter_kit/src/features/profile/logic/profile_logic.dart';
import 'package:flutter_kit/src/base/widgets/view_state_widget.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:provider/provider.dart';

/// 我的页面主视图 - 与 ProfileLogic 结合
class ProfilePageView extends StatelessWidget {
  const ProfilePageView({super.key});

 

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => locator<ProfileLogic>(),
      child: Consumer<ProfileLogic>(
        builder: (context, logic, child) {
          return Scaffold(
            backgroundColor: AppColors.pageBackground,
            body: Stack(
              children: [
                // 背景渐变 - 延伸到状态栏
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 200.h + MediaQuery.of(context).padding.top,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColors.primary.withValues(alpha: 0.08),
                          AppColors.primary.withValues(alpha: 0.04),
                          AppColors.primary.withValues(alpha: 0.02),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
                // 主要内容
                SafeArea(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(height: 16.h), // 顶部间距
                        _buildUserCard(context, logic),
                        SizedBox(height: 12.h),
                        _buildStatsCard(logic),
                        SizedBox(height: 12.h),
                        _buildResumeCard(context),
                        SizedBox(height: 12.h),
                        _buildMenuList(logic),
                        SizedBox(height: 24.h),
                        _buildLogoutButton(logic),
                        SizedBox(height: 24.h),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }



  /// 构建用户信息卡片
  Widget _buildUserCard(BuildContext context, ProfileLogic logic) {
    return Container(
      margin: EdgeInsets.fromLTRB(0, 12.w, 0, 0),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        children: [
          // 头像
          CircleAvatar(
            radius: 36.w,
            backgroundImage: logic.isLoggedIn && logic.userAccount?.headPic?.isNotEmpty == true
                ? NetworkImage(logic.userAccount!.headPic!)
                : const AssetImage('assets/images/ic_head_kt1.png') as ImageProvider,
          ),
          SizedBox(width: 20.w),
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 显示加载状态或用户名
                logic.isLoading
                    ? Row(
                        children: [
                          SizedBox(
                            width: 16.w,
                            height: 16.w,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.w,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            '加载中...',
                            style: TextStyle(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.w700,
                              color: AppColors.textSecondary,
                              height: 1.2,
                            ),
                          ),
                        ],
                      )
                    : Text(
                        logic.isLoggedIn
                            ? (logic.userAccount?.nickName ?? logic.userAccount?.realName ?? '未设置昵称')
                            : '未登录',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.textPrimary,
                          height: 1.2,
                        ),
                      ),
                SizedBox(height: 6.h),
                GestureDetector(
                  onTap: () {
                    // 如果未登录，跳转到登录页面
                    if (!logic.isLoggedIn) {
                      context.router.push(LoginRoute());
                    } else {
                      // TODO: 处理编辑资料点击
                    }
                  },
                  child: Row(
                    children: [
                      Text(
                        logic.isLoggedIn
                            ? ('完善资料，获得更多机会')
                            : '点击登录',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12.w,
                        color: AppColors.textSecondary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计数据卡片
  Widget _buildStatsCard(ProfileLogic logic) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(vertical: 20.h),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            logic.isLoggedIn ? '${logic.userAccount?.jobCollectsCount ?? 0}' : '0',
            '收藏职位',
          ),
          _buildStatItem(
            logic.isLoggedIn ? '${logic.userAccount?.enterpriseCollectsCount ?? 0}' : '0',
            '关注企业',
          ),
          _buildStatItem(
            logic.isLoggedIn ? '${logic.userAccount?.jobApplysCount ?? 0}' : '0',
            '申请记录',
          ),
          _buildStatItem(
            logic.isLoggedIn ? '${logic.userAccount?.jobApplysCount ?? 0}' : '0',
            '面试邀请',
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String count, String label) {
    return GestureDetector(
      onTap: () {
        // TODO: 处理统计项点击
      },
      child: Column(
        children: [
          Text(
            count,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建简历卡片 - 重新设计
  Widget _buildResumeCard(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          // 头部区域
          Padding(
            padding: EdgeInsets.fromLTRB(20.w, 20.h, 20.w, 16.h),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '我的简历',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 左右布局：简历库和附件简历
          Padding(
            padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.h),
            child: Row(
              children: [
                // 简历库
                Expanded(
                  child: _buildResumeTypeCard(
                    iconPath: 'assets/images/ic_resume.png',
                    title: '简历库',
                    count: '1份',
                    onTap: () {
                      // 跳转到简历库页面
                      context.router.push(const ResumeLibraryRoute());
                    },
                  ),
                ),
                SizedBox(width: 12.w),
                // 附件简历
                Expanded(
                  child: _buildResumeTypeCard(
                    iconPath: 'assets/images/ic_annex.png',
                    title: '附件简历',
                    count: '0份',
                    onTap: () {
                      // TODO: 处理附件简历点击
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建简历类型卡片 - 重新设计
  Widget _buildResumeTypeCard({
    required String iconPath,
    required String title,
    required String count,
    required VoidCallback onTap,
  }) {
    // 根据不同类型设置不同的渐变色
    final List<Color> gradientColors = [
      AppColors.primaryLight.withValues(alpha: 0.1),
      AppColors.primaryLight.withValues(alpha: 0.2),
      AppColors.primaryLight.withValues(alpha: 0.4),
    ];

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradientColors,
          ),
          borderRadius: BorderRadius.circular(16.r),

        ),
        child: Row(
          children: [
            // 左侧图标
            Container(
              width: 30.w,
              height: 30.w,
              child: Image.asset(
                iconPath,
                width: 30.w,
                height: 30.w,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 12.w),
            // 右侧文字
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    count,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建菜单列表
  Widget _buildMenuList(ProfileLogic logic) {
    final menuItems = logic.menuItems;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: menuItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == menuItems.length - 1;

          return _buildMenuItem(
            title: item.title,
            icon: item.icon,
            isLast: isLast,
            onTap: () {
              // TODO: 处理菜单项点击
            },
          );
        }).toList(),
      ),
    );
  }

  /// 构建菜单项
  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required bool isLast,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 18.h),
        decoration: BoxDecoration(
          border: isLast ? null : Border(
            bottom: BorderSide(
              color: AppColors.borderColor.withValues(alpha: 0.5),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 22.w,
              color: AppColors.textSecondary,
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 14.w,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建退出登录按钮
  Widget _buildLogoutButton(ProfileLogic logic) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            logic.logout();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.cardBackground,
            foregroundColor: AppColors.textSecondary,
            padding: EdgeInsets.symmetric(vertical: 16.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16.r),
              side: BorderSide(
                color: AppColors.borderColor.withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            elevation: 0,
          ),
          child: Text(
            '退出登录',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}
