import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/features/base_info/logic/base_info_logic.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../resume/theme/resume_theme.dart';
import '../../../resume/ui/widget/resume_pickers.dart';
import '../../../resume/ui/widget/selectable_item.dart';
import '../../../resume/ui/widget/text_edit_dialog.dart';
import 'build_divider.dart';
import 'build_upload_avatar_option.dart';
import 'get_image_provider.dart';

class BaseInfoPageView extends StatefulWidget {
  const BaseInfoPageView({super.key})

  @override
  State<StatefulWidget> createState() {
    return _BaseinfoPageViewState();
  }
}

class _BaseinfoPageViewState extends State<BaseInfoPageView> {
  late BaseInfoEntity _baseInfoEntity;

  @override
  void initState() {
    super.initState();
    //   调用接口，获取数据

  }


  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(create:
        (_) => locator<BaseInfoLogic>(),
      child: Consumer<BaseInfoLogic>(
        builder: (context, logic, child) {
          return Stack()
        },
      ),
    );
  }


//构建头部信息
  Widget _buildPersonalInfoSection() {
    return _buildStyledCard(
      title: '个人信息',
      child: Column(
        children: [
          _buildAvatarRow(),
          buildDivider(),
          SelectableItem(
            label: '姓名',
            value: _baseInfoEntity.name,
            hint: '请输入姓名',
            hintColor: const Color(0xFFCCCCCC),
            isRequired: true,
            onTap: () {
              TextEditDialog.show(
                context: context,
                title: '编辑姓名',
                initialText: _baseInfoEntity.name,
                maxLength: 20,
                maxLines: 1,
                hintText: '请输入您的姓名',
                heightRatio: 0.7,
                // 姓名编辑使用较小高度70%
                onSave: (text) {
                  setState(() {
                    _baseInfoEntity.name = text;
                  });
                },
              );
            },
          ),
          buildDivider(),
          ResumePickers.genderPicker(
            context: context,
            currentValue: _baseInfoEntity.genderCode,
            onChanged: (value) {
              setState(() => _resumeData.gender = value);
            },
          ),
          _buildDivider(),
          ResumePickers.birthYearPicker(
            context: context,
            currentValue: _resumeData.birthYear,
            onChanged: (value) {
              setState(() => _resumeData.birthYear = value);
            },
          ),
          _buildDivider(),
          ResumePickers.jobStatusPicker(
            context: context,
            currentValue: _resumeData.jobStatus,
            onChanged: (value) {
              setState(() => _resumeData.jobStatus = value);
            },
          ),
        ],
      ),
    );
  }


  /// 构建头像行
  Widget _buildAvatarRow() {
    return GestureDetector(
      onTap: () {
        _showAvatarPicker();
      },
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '头像',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: ResumeTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 7.h),
                  Text(
                    '点击修改头像',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加间距
            Container(
              width: 56.w,
              height: 56.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: getImageProvider(''),
                  fit: BoxFit.cover,
                ),
                border: Border.all(
                  color: ResumeTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


  /// 显示头像选择器
  void _showAvatarPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: ResumeTheme.surfaceColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部标题
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(color: ResumeTheme.textSecondary),
                      ),
                    ),
                    Text(
                      '选择头像',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: ResumeTheme.textPrimary,
                      ),
                    ),
                    SizedBox(width: 48.w), // 占位，保持标题居中
                  ],
                ),
              ),
              Container(height: 1.h, color: ResumeTheme.borderColor),

              // 默认头像选项
              Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildAvatarOption(
                          'assets/images/ic_head_kt1.png',
                          isAsset: true,
                        ),
                        _buildAvatarOption(
                          'assets/images/ic_head_kt2.png',
                          isAsset: true,
                        ),
                        buildUploadAvatarOption(context),
                      ],
                    ),
                    SizedBox(height: 30.h),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 统一样式的卡片容器
  Widget _buildStyledCard({
    required String title,
    required Widget child,
    String? actionText,
    VoidCallback? onActionTap,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: ResumeTheme.textPrimary,
                  ),
                ),
                if (actionText != null)
                  GestureDetector(
                    onTap: onActionTap,
                    child: Text(
                      actionText,
                      style: TextStyle(
                        color: ResumeTheme.primaryColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: ResumeTheme.borderColor,
          ),
          child,
        ],
      ),
    );
  }

  /// 构建头像选项
  Widget _buildAvatarOption(String imagePath, {bool isAsset = false}) {
    // final isSelected = _baseInfoEntity.a == imagePath;
    return GestureDetector(
      onTap: () {
        setState(() {
          // _resumeData.avatarUrl = imagePath;
        });
        Navigator.pop(context);
      },
      child: Column(
        children: [
          Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: isAsset
                    ? AssetImage(imagePath) as ImageProvider
                    : NetworkImage(imagePath),
                fit: BoxFit.cover,
              ),
              border: Border.all(
                // color: isSelected ? ResumeTheme.primaryColor : ResumeTheme
                //     .borderColor,
                // width: isSelected ? 3 : 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

}