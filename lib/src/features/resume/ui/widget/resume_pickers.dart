import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'selectable_item.dart';
import 'common_picker.dart';
import '../../../../shared/config/managers/config_manager.dart';
import '../../../../shared/config/models/config_models.dart';

/// 简历选择器组件集合
class ResumePickers {
  /// 性别选择器
  static Widget genderPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '性别',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {

        // 将当前的key转换为显示值
        String? displayValue = currentValue;
        if (currentValue != null && currentValue.isNotEmpty) {
          final convertedValue = await ConfigManager().getValueByKey('Gender', currentValue);
          if (convertedValue != null) {
            displayValue = convertedValue;
          }
        }

        final result = await CommonPicker.show(
          context: context,
          title: '选择性别',
          options: await ConfigManager().getGroupValues('Gender') ?? [],
          currentValue: displayValue,
        );

        if (result != null) {
          // 将选择的显示值转换为key
          final selectedKey = await ConfigManager().getKeyByValue('Gender',result);
          if (selectedKey != null) {
            onChanged(selectedKey);
          }
        }
      },
    );
  }

  /// 出生年份选择器
  static Widget birthYearPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '出生年份',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        final years = List.generate(50, (index) => '${2024 - index}年');
        final result = await CommonPicker.show(
          context: context,
          title: '选择出生年份',
          options: years,
          currentValue: currentValue,
        );
        if (result != null) {
          onChanged(result);
        }
      },
    );
  }

  /// 求职身份选择器
  static Widget jobStatusPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '求职身份',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        const options = [
          '在校-找暑期实习',
          '在校-找全职',
          '离职-随时到岗',
          '在职-月内到岗',
          '在职-考虑机会',
        ];
        final result = await CommonPicker.show(
          context: context,
          title: '选择求职身份',
          options: options,
          currentValue: currentValue,
        );
        if (result != null) {
          onChanged(result);
        }
      },
    );
  }

  /// 工作性质选择器
  static Widget jobNaturePicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '工作性质',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        // 从配置管理器获取工作性质选项
        final configManager = ConfigManager();
        final jobNatureOptions = await configManager.getJobNatureOptions();

        if (jobNatureOptions == null || jobNatureOptions.isEmpty) {
          // 如果配置加载失败，使用默认选项
          final result = await CommonPicker.show(
            context: context,
            title: '选择工作性质',
            options: const ['全职', '兼职', '临时工', '小时工'],
            currentValue: currentValue,
          );
          if (result != null) {
            onChanged(result);
          }
          return;
        }

        // 将当前的key转换为显示值
        String? displayValue;
        if (currentValue != null && currentValue!.isNotEmpty) {
          displayValue = await configManager.getJobNatureValue(currentValue!);
        }

        final result = await CommonPicker.show(
          context: context,
          title: '选择工作性质',
          options: jobNatureOptions.values.toList(),
          currentValue: displayValue,
        );

        if (result != null) {
          // 将选择的显示值转换为key
          final selectedKey = await configManager.getJobNatureKey(result);
          if (selectedKey != null) {
            onChanged(selectedKey);
          }
        }
      },
    );
  }

  /// 姓名输入项
  static Widget nameInput({
    required String? currentValue,
    required Function(String) onChanged,
    required VoidCallback onTap,
  }) {
    return SelectableItem(
      label: '姓名',
      value: currentValue,
      hint: '请输入姓名',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: onTap,
    );
  }

}

/// 简历选择器工具类
class ResumePickerUtils {
  /// 生成出生年份选项
  static List<String> generateBirthYears({int startYear = 1950, int? endYear}) {
    final currentYear = endYear ?? DateTime
        .now()
        .year;
    return List.generate(
      currentYear - startYear + 1,
          (index) => '${currentYear - index}年',
    );
  }

  /// 求职身份选项
  static const List<String> jobStatusOptions = [
    '在校-找暑期实习',
    '在校-找全职',
    '离职-随时到岗',
    '在职-月内到岗',
    '在职-考虑机会',
  ];

  /// 性别选项
  static const List<String> genderOptions = ['男', '女'];
}
